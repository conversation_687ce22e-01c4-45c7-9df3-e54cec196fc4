import CheckIcon from "@/components/icon/CheckIcon";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import LoginPopup from "@/components/LoginPopup";
import { SelectedVoucher } from "@/components/voucher/CheckVoucherType";
import VoucherItem, { VoucherItemCategory } from "@/components/voucher/VourcherItem";
import { Icon } from "@/constants/Assets";
import { ReleaseType, RewardType, StatusDelivery, VoucherType } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { COLORS } from "@/constants/themes";
import { useCart } from "@/hooks/useCart";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { getUser, updateUserPoint } from "@/redux/slices/authen/authSlice";
import { getProductCategoryList, getProductDetailById } from "@/redux/slices/product/productSlice";
import {
  collectVoucher,
  collectVoucherPoint,
  getDetailVoucherByCode,
  getDetailVoucherByCodePublic,
  VoucherDto,
} from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { showToast } from "@/utils/common";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Modal,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";

export default function VoucherDetail() {
  const location = useLocation();
  const [itemsOfVoucher, setItemsOfVoucher] = React.useState<string[]>([]);
  const [itemsCategoryOfVoucher, setItemsCategoryOfVoucher] = React.useState<string>("");
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [hasInitialized, setHasInitialized] = useState(false);

  const appConfig = useConfigApp();
  const { color } = useConfigApp();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { checkLogin, openLoginPopup, setOpenLoginPopup, onClickRegister, loading } =
    useCheckLogin();
  const {
    // myVoucherUserList,
    listVoucherByUser,
  } = useSelector((state: RootState) => state.vouchers);
  const { showAlert } = useAlert();
  const { user } = useSelector((state: RootState) => state.auth);
  const { voucherDetail } = useSelector((state: RootState) => state.vouchers);
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [message, setMessage] = useState<string>("");

  // const bannerUri =
  //   voucher.banner?.formats?.thumbnails?.url ||
  //   voucher.banner?.formats?.small?.url ||
  //   voucher.banner?.url ||
  //   undefined;
  const { code } = useParams();

  const getVoucherDetail = async () => {
    if (!code) return;

    if (user) {
      // Nếu đã đăng nhập, gọi API có authentication
      await dispatch(getDetailVoucherByCode(code));
    } else {
      // Nếu chưa đăng nhập, gọi API public
      await dispatch(getDetailVoucherByCodePublic(code));
    }
  };

  // Initial load effect - only runs once when component mounts
  useEffect(() => {
    if (!hasInitialized && code) {
      setHasInitialized(true);
      getVoucherDetail()
        .then(() => {
          setInitialLoading(false);
        })
        .catch(() => {
          setInitialLoading(false);
        });
    }
  }, [code, hasInitialized]);

  // User change effect - only refetch if user changes after initial load
  useEffect(() => {
    if (hasInitialized && code) {
      getVoucherDetail();
    }
  }, [user]);

  const collectVoucherP = async () => {
    if (!code) return;

    checkLogin(async () => {
      const res = await dispatch(collectVoucherPoint(code));
      if (res?.payload?.success) {
        if (res?.payload?.data?.newPoint !== undefined) {
          dispatch(updateUserPoint(res.payload.data.newPoint));
        } else {
          await dispatch(getUser());
        }
        showAlert({
          title: "Chúc mừng",
          content: res?.payload?.message,
          buttons: [
            {
              title: "Đóng",
            },
          ],
        });
        getVoucherDetail();
      }
    });
  };

  const detail = voucherDetail?.voucherDetails?.[0];
  const numUse = detail?.numUse ?? null;
  const isCollected = user ? numUse !== null : false; // Chỉ kiểm tra khi đã đăng nhập
  const isMyVoucher = user ? isCollected && (numUse ?? 0) > 0 : false; // Chỉ kiểm tra khi đã đăng nhập
  const isDisableVoucher =
    (isCollected && (numUse ?? 0) <= 0) || // Đã thu thập nhưng hết lượt
    voucherDetail?.quantity === voucherDetail?.quantityUsed; // Hết số lượng phát hành

  const isHaveInMyVoucher = user
    ? !!listVoucherByUser?.find((item) => item.voucherId === voucherDetail?.voucherId)
    : false;

  // Cập nhật logic xác định button text
  let btnText = "Lưu";
  if (isDisableVoucher) {
    btnText = "Đã hết lượt dùng";
  } else if (isMyVoucher) {
    btnText = "Dùng ngay";
  } else if (voucherDetail?.exchangePoints && voucherDetail.exchangePoints > 0) {
    btnText = `Đổi ${voucherDetail.exchangePoints} điểm`;
  }

  const handleExchangeVoucher = async () => {
    if (voucherDetail?.releaseType === ReleaseType.Free) return;

    checkLogin(() => {
      if (!user?.point) {
        toast.error("Người dùng chưa có điểm khuyến mại");
        return;
      }
      if (user.point < (voucherDetail?.exchangePoints || 0)) {
        toast.error("Điểm của người dùng không đủ");
        return;
      }

      setOpenConfirmDialog(true);
    });
  };

  const handleConfirmExchange = async () => {
    try {
      if (!code) return;
      const res = await dispatch(collectVoucher(code ?? ""));

      if (res?.payload?.status === 400) {
        toast.error(res?.payload?.detail);
      } else {
        toast.success("Đổi voucher thành công");
        await dispatch(getUser());
        // Redirect về trang voucher
        navigate(Router.voucher.index);
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi đổi voucher");
    } finally {
      setOpenConfirmDialog(false);
    }
  };

  const handleCloseConfirmDialog = () => {
    setOpenConfirmDialog(false);
  };

  const onChangePointToVoucher = async () => {
    try {
      if (!code) return;

      checkLogin(async () => {
        const res = await dispatch(collectVoucher(code ?? ""));
        console.log(res.payload);

        if (res?.payload?.status === 400) {
          toast.error(res?.payload?.detail);
        } else {
          if (
            voucherDetail?.voucherType === VoucherType.Custom &&
            voucherDetail?.rewardType === RewardType.Point
          ) {
            getVoucherDetail();
          } else {
            navigate(Router.voucher.index);
          }
          toast.success("Lưu voucher thành công");
        }
      });
    } catch (error) {}
  };

  const onApplyClick = () => {
    if (voucherDetail?.exchangePoints && voucherDetail.exchangePoints > 0) {
      handleExchangeVoucher();
    } else {
      checkLogin(() => {
        showAlert({
          title: "Lưu Voucher",
          content: "Bạn có chắc chắn muốn lưu voucher này không?",
          icon: (
            <CheckIcon
              primaryColor={appConfig.color.primary}
              secondaryColor={appConfig.bgColor.primary}
            />
          ),
          buttons: [
            {
              title: "Huỷ",
            },
            {
              title: "Xác nhận",
              action: onChangePointToVoucher,
            },
          ],
        });
      });
    }
  };

  const fetListItemOfVoucher = async () => {
    if (!voucherDetail) return;
    if (voucherDetail?.productIds.length > 0) {
      const results = await Promise.all(
        voucherDetail?.productIds.map(async (itemId) => {
          const res = await dispatch(getProductDetailById(itemId));
          return res?.payload?.itemsName;
        })
      );
      setItemsOfVoucher(results);
    }
  };

  const fetListItemCategoryOfVoucher = async () => {
    if (!voucherDetail) return;
    try {
      const res = await dispatch(getProductCategoryList()).unwrap();
      if (!res?.data) {
        return;
      }
      // Hỗ trợ cả categoryIds (mảng) và categoryId (đơn lẻ)
      let categoryNames = "";
      if (
        voucherDetail?.categoryIds &&
        Array.isArray(voucherDetail.categoryIds) &&
        voucherDetail.categoryIds.length > 0
      ) {
        const filteredItems = res.data.filter((item: any) =>
          voucherDetail.categoryIds?.includes(item.categoryId)
        );
        categoryNames = filteredItems.map((item: any) => item.categoryName).join(", ");
      }
      setItemsCategoryOfVoucher(categoryNames);
    } catch (error) {}
  };

  useEffect(() => {
    fetListItemOfVoucher();
    fetListItemCategoryOfVoucher();
  }, [voucherDetail]);

  const handleCloseModal = () => {
    setIsOpenModal(false);
  };
  const { cartPayment, setCartAndSave } = useCart();
  const handleSelectVoucher = async (voucher: VoucherDto) => {
    try {
      checkLogin(async () => {
        if (cartPayment.listItems.length > 0) {
          const voucherCode = voucher?.voucherDetails?.[0]?.voucherCode;
          if (voucher.voucherType === VoucherType.Transport) {
            if (cartPayment.statusDelivery === StatusDelivery.InHome) {
              await setCartAndSave({
                ...cartPayment,
                voucherTransport: [voucher],
                voucherCodes: voucherCode ? [voucherCode] : [],
              });
            } else {
              await setCartAndSave({
                ...cartPayment,
                statusDelivery: StatusDelivery.InHome,
                voucherTransport: [voucher],
                voucherCodes: voucherCode ? [voucherCode] : [],
              });
            }
          } else {
            await setCartAndSave({
              ...cartPayment,
              voucherPromotion: [voucher],
              voucherCodes: voucherCode ? [voucherCode] : [],
            });
          }
          navigate(Router.cartPayment);
        } else {
          showToast({
            content: "Vui lòng chọn sản phẩm trước khi áp dụng mã giảm giá",
            type: "error",
          });
        }
      });
    } catch (error: any) {
      if (error.status === 400) {
        navigate(Router.menu);
      }
    }
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <>
      <Skeleton variant="rectangular" width="100%" height={200} sx={{ mb: 2 }} />
      <Stack marginInline={2} marginTop={-7}>
        <Stack className="voucherDetail-profile" sx={{ mb: 2 }}>
          <Skeleton variant="rectangular" width="100%" height={120} sx={{ borderRadius: 2 }} />
        </Stack>
        <Stack p={1.6} marginBlock={0}>
          <Skeleton variant="text" width="40%" height={24} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="80%" height={20} />
          <Skeleton variant="text" width="60%" height={20} />
        </Stack>
        <Stack p={1.6} marginBlock={0}>
          <Skeleton variant="text" width="50%" height={24} sx={{ mb: 1 }} />
          <Skeleton variant="text" width="70%" height={20} />
        </Stack>
      </Stack>
      <Stack className="bottomElementPreventScroll" style={styles.bottomBtnContainer}>
        <Skeleton variant="rectangular" width="95%" height={48} sx={{ ml: 1.5, borderRadius: 1 }} />
      </Stack>
    </>
  );

  // Ref for scrollable content
  const contentRef = useRef<HTMLDivElement>(null);

  return (
    <FrameContainerFull
      title="Thông tin voucher"
      overrideStyle={{
        background: color.secondary,
        height: "100vh",
        overflow: "hidden",
      }}
    >
      <Box
        ref={contentRef}
        sx={{
          height: "calc(100vh - 60px)",
          overflowY: "auto",
          paddingBottom: "300px", // Add padding to ensure content is visible above the fixed button
        }}
      >
        {initialLoading ? (
          <LoadingSkeleton />
        ) : !voucherDetail ? (
          <Stack
            sx={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              minHeight: "60vh",
              p: 4,
              mx: 2,
              mt: 4,
              borderRadius: 3,
              backgroundColor: "white",
              boxShadow: "0 4px 20px rgba(0,0,0,0.08)",
              textAlign: "center",
              gap: 3,
            }}
          >
            {/* Sad Icon */}
            <Box
              sx={{
                width: 120,
                height: 120,
                borderRadius: "50%",
                backgroundColor: "#f8f9fa",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mb: 1,
              }}
            >
              <img
                src={Icon.sad}
                alt="Voucher not found"
                style={{
                  width: 80,
                  height: 80,
                  opacity: 0.8,
                }}
              />
            </Box>

            {/* Main Message */}
            <Stack spacing={1} alignItems="center">
              <Typography
                variant="h6"
                fontWeight="600"
                color={COLORS.neutral2}
                sx={{ fontSize: { xs: 18, sm: 20 } }}
              >
                Oops! Voucher không tìm thấy
              </Typography>
              <Typography
                variant="body1"
                color={COLORS.neutral5}
                sx={{
                  maxWidth: 280,
                  lineHeight: 1.5,
                  fontSize: { xs: 14, sm: 15 },
                }}
              >
                Voucher này có thể đã hết hạn, đã được sử dụng hết hoặc không tồn tại.
              </Typography>
            </Stack>

            {/* Action Buttons */}
            <Stack spacing={2} width="100%" maxWidth={280}>
              <Button
                variant="contained"
                onClick={() => navigate(Router.voucher.index)}
                sx={{
                  py: 1.5,
                  borderRadius: 2,
                  backgroundColor: appConfig.color.primary,
                  color: appConfig.color.accent,
                  fontWeight: 600,
                  fontSize: 16,
                  textTransform: "none",
                  boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                  "&:hover": {
                    backgroundColor: appConfig.color.primary,
                    opacity: 0.9,
                    boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                  },
                }}
              >
                Xem tất cả Voucher
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate(Router.homepage)}
                sx={{
                  py: 1.5,
                  borderRadius: 2,
                  borderColor: COLORS.neutral8,
                  color: COLORS.neutral4,
                  fontWeight: 500,
                  fontSize: 15,
                  textTransform: "none",
                  "&:hover": {
                    borderColor: appConfig.color.primary,
                    color: appConfig.color.primary,
                    backgroundColor: "transparent",
                  },
                }}
              >
                Về trang chủ
              </Button>
            </Stack>
          </Stack>
        ) : (
          <>
            <img
              style={styles.banner}
              src={voucherDetail.image.link ?? "/demo/images/banner.png"}
            />
            <Stack marginInline={2} marginTop={-7}>
              {voucherDetail && (
                <Stack className="voucherDetail-profile">
                  <VoucherItem item={voucherDetail} category={VoucherItemCategory.DETAIL} />
                </Stack>
              )}
              <Stack p={1.6} marginBlock={0}>
                <Typography
                  style={{
                    ...styles.titleStyle,
                    color: appConfig.color.primary,
                  }}
                >
                  Thông tin ưu đãi
                </Typography>
                <Typography>
                  Lượt sử dụng có hạn. Nhanh tay kẻo lỡ bạn nhé!{" "}
                  {voucherDetail && SelectedVoucher(voucherDetail)}
                </Typography>
              </Stack>
              {itemsCategoryOfVoucher && (
                <Stack p={1.6} marginBlock={0}>
                  <Typography
                    style={{
                      ...styles.titleStyle,
                      color: appConfig.color.primary,
                    }}
                  >
                    Áp dụng cho danh mục sản phẩm
                  </Typography>
                  <Typography>
                    Chỉ áp dụng cho loại sản phẩm:{" "}
                    <Typography sx={{ fontWeight: 600, display: "inline" }}>
                      {itemsCategoryOfVoucher}
                    </Typography>
                  </Typography>
                </Stack>
              )}
              {itemsOfVoucher.length > 0 && (
                <Stack p={1.6} marginBlock={0}>
                  <Typography
                    style={{
                      ...styles.titleStyle,
                      color: appConfig.color.primary,
                    }}
                  >
                    Áp dụng cho sản phẩm
                  </Typography>
                  <Typography>
                    Chỉ áp dụng cho một số sản phẩm nhất định:{" "}
                    <Typography sx={{ fontWeight: 600, display: "inline" }}>
                      {itemsOfVoucher.join(", ")}
                    </Typography>
                  </Typography>
                </Stack>
              )}
            </Stack>
          </>
        )}
      </Box>

      {voucherDetail && (
        <Box
          sx={{
            ...styles.fixedButtonContainer,
          }}
        >
          <Button
            variant="contained"
            size="small"
            style={{
              ...styles.bottomBtn,
              color: appConfig.color.accent,
              backgroundColor: isDisableVoucher ? COLORS.neutral6 : appConfig.color.primary,
            }}
            disabled={isDisableVoucher}
            onClick={() => {
              if (!user) {
                checkLogin(() => {});
                return;
              }

              if (isMyVoucher) {
                if (
                  voucherDetail?.voucherType === VoucherType.Custom &&
                  voucherDetail.rewardType === RewardType.Point
                ) {
                  collectVoucherP();
                } else {
                  handleSelectVoucher(voucherDetail);
                }
              } else {
                onApplyClick();
              }
            }}
          >
            {btnText}
          </Button>
        </Box>
      )}

      {/* Dialog xác nhận đổi điểm */}
      <Dialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        aria-labelledby="confirm-exchange-dialog"
      >
        <DialogTitle id="confirm-exchange-dialog">Xác nhận đổi voucher</DialogTitle>
        <DialogContent>
          <Typography>
            Bạn có chắc chắn muốn đổi {voucherDetail?.exchangePoints} điểm để lấy voucher này không?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirmDialog} color="primary" variant="outlined">
            Hủy
          </Button>
          <Button
            onClick={handleConfirmExchange}
            color="primary"
            variant="contained"
            sx={{ color: "white" }}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>

      <Modal
        open={isOpenModal}
        onClose={handleCloseModal}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <Box sx={styles.modalBox}>
          <Typography id="modal-title" variant="h6" component="h2" mb={2}>
            Thông báo
          </Typography>
          <Typography id="modal-description" sx={{ mt: 2 }}>
            {message}
          </Typography>
          <Button
            onClick={handleCloseModal}
            sx={{ mt: 2 }}
            variant="contained"
            style={{
              backgroundColor: appConfig.color.primary,
              color: appConfig.color.accent,
            }}
          >
            Đóng
          </Button>
        </Box>
      </Modal>

      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties | any> = {
  banner: {
    objectFit: "cover",
    width: "100%",
    aspectRatio: 1,
  },
  fixedButtonContainer: {
    position: "fixed",
    bottom: 15,
    left: 0,
    right: 0,
    width: "100%",
    maxWidth: 450,
    zIndex: 100,
    display: "flex",
    justifyContent: "center",
    padding: "0 16px",
    margin: "0 auto",
  },
  bottomBtn: {
    width: "100%",
    padding: 12,
    fontSize: 17,
    borderRadius: 8,
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: 700,
    marginBottom: "10px",
  },
  modalBox: {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: 300,
    backgroundColor: "white",
    border: "2px solid #000",
    boxShadow: "24px",
    padding: 20,
    borderRadius: 2,
    textAlign: "center",
  },
};

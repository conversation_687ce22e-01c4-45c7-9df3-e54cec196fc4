import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, Typography, Button, Card, Grid, Skeleton } from "@mui/material";
import { AppDispatch, RootState } from "@/redux/store";
import { useNavigate } from "@/utils/component-util";
import { Router } from "@/constants/Route";

import { getProductListByCategory } from "@/redux/slices/product/productListSlice";
import { getListVoucherByUser } from "@/redux/slices/voucher/voucherSlice";
import { useConfigApp } from "@/hooks/useConfigApp";
import Banner from "@/components/home/<USER>";
import VoucherListSlide from "@/components/voucher/VoucherListSlide";
import ProductListGridSlice from "@/components/home/<USER>";
import VoucherListColumn from "@/components/voucher/VoucherListColumn";
import { keyComponent } from "@/types/home";
import ListItem from "@/components/home/<USER>";
import BtnAction from "@/components/common/BtnAction";
import ProductListGridSliceV2 from "@/components/home/<USER>/ProductListGridSliceRetail";
import NewsItem from "@/components/news/NewsItem";
import NoDataView from "@/components/UI/NoDataView";
import ArrowRightIcon from "@/components/icon/ArrowRightIcon";
import { INews } from "@/types/news";
import { getListNewsUser } from "@/redux/slices/news/newsListSlice";
import ListSlider from "@/components/home/<USER>";
import NewsItemHorizontalV2 from "@/components/news/newsitemhorizontal/NewsItemHorizontalRetail";
import { COLORS } from "@/constants/themes";
import ListNews from "@/components/news/ListNews";

interface TabsSectionProps {
  activeTab: "enter-code" | "rewards";
  onTabChange: (tab: "enter-code" | "rewards") => void;
  onRewardClick: () => void;
  onNavigateToVoucherTab: (tab: number) => void;
  onEnterCodeClick: () => void;
}

function TabsSection({
  onRewardClick,
  onNavigateToVoucherTab,
  onEnterCodeClick,
}: TabsSectionProps) {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { userVoucherCount } = useSelector((state: RootState) => state.vouchers);

  // Fetch user's vouchers when component mounts
  useEffect(() => {
    if (user && shopId) {
      dispatch(
        getListVoucherByUser({
          PageIndex: 0,
          PageSize: 1,
          shopId,
        })
      );
    }
  }, [user, shopId]);
  return (
    <Box
      sx={{
        mb: 2,
        mt: -4,
        bgcolor: "rgba(255, 255, 255, 0.95)",
        backdropFilter: "blur(8px)",
        borderRadius: 2,
        mx: 2,
        boxShadow:
          "rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;",
      }}
    >
      <Card sx={{ boxShadow: 0, bgcolor: "transparent", position: "relative" }}>
        <Grid container>
          <Grid item xs={6} sx={{ borderRight: "2px dashed", borderColor: "grey.300" }}>
            <Box
              onClick={onEnterCodeClick}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                paddingBlock: 1,
                paddingInline: 2,
                cursor: "pointer",
                transition: "background-color 0.2s ease",
              }}
            >
              <img src="icons/voucher.png" height={30} width={30} alt="voucher" />
              <Box>
                <Typography variant="body2" fontWeight={600}>
                  Nhập mã
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.7, fontSize: 12 }}>
                  Mã ưu đãi
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={6}>
            <Box
              onClick={() => {
                onRewardClick();
                // Navigate to tab 1 (Voucher của tôi)
                onNavigateToVoucherTab(1);
              }}
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
                paddingBlock: 1,
                paddingInline: 2,
                cursor: "pointer",
              }}
            >
              <img src="icons/gift.png" height={30} width={30} alt="gift" />
              <Box>
                <Typography variant="body2" fontWeight={600}>
                  Phần thưởng
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.7, fontSize: 12 }}>
                  Có {userVoucherCount} ưu đãi
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Card>
    </Box>
  );
}

// Main VoucherApp Component
export function VoucherApp() {
  const [activeTab, setActiveTab] = useState<"enter-code" | "rewards">("enter-code");
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { promotion } = useConfigApp();
  const [listArticle, setListArticle] = useState<INews[]>([]);
  const [isLoadingNews, setIsLoadingNews] = useState(false);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const appConfig = useConfigApp();

  useEffect(() => {
    dispatch(
      getProductListByCategory({
        skip: 0,
        limit: 20,
        parentId: "root",
      })
    );
  }, []);

  const handleRewardClick = () => {
    // Navigate to voucher page with tab 1 (Voucher của tôi)
    navigate(`${Router.voucher.index}?tab=1`);
  };

  const handleNavigateToVoucherTab = (tab: number) => {
    // Navigate to voucher page with specific tab
    navigate(`${Router.voucher.index}?tab=${tab}`);
  };

  const handleEnterCodeClick = () => {
    // Navigate to voucher code entry page
    navigate(Router.voucher.enterCode);
  };

  const onNavigateToPost = (title?: string) => {
    navigate(Router.post.index, { state: { title } });
  };

  const fetchListNewsUser = async () => {
    if (shopId) {
      setIsLoadingNews(true);
      try {
        const res = await dispatch(getListNewsUser(shopId));
        setListArticle(res?.payload?.data);
      } finally {
        setIsLoadingNews(false);
      }
    }
  };
  useEffect(() => {
    fetchListNewsUser();
  }, [shopId]);

  const renderNewsSkeletonLoading = (isSlice = false) => {
    const imageHeight = 110;
    const borderRadius = 1;
    const titleHeight = 20;
    const dateHeight = 14;
    if (isSlice) {
      return (
        <Box sx={{ width: "100%", px: 0 }}>
          <Box
            sx={{
              display: "flex",
              gap: 1.5,
              overflow: "hidden",
            }}
          >
            {[...Array(1)].map((_, index) => (
              <Box
                key={index}
                sx={{
                  minWidth: "100%",
                  flexShrink: 0,
                  borderRadius: borderRadius,
                  p: 0.5,
                }}
              >
                <Skeleton
                  variant="rectangular"
                  height={imageHeight}
                  sx={{ borderRadius: borderRadius }}
                />
                <Skeleton variant="text" width="80%" height={titleHeight} sx={{ mt: 1 }} />
                <Skeleton
                  variant="text"
                  width="50%"
                  height={dateHeight}
                  sx={{ mt: 0.5, bgcolor: "grey.200" }}
                />
              </Box>
            ))}
          </Box>
        </Box>
      );
    }

    return (
      <Grid container spacing={2} sx={{ width: "100%", margin: 0 }}>
        {[...Array(2)].map((_, index) => (
          <Grid item xs={6} key={index}>
            <Box sx={{ mb: 1.5, borderRadius: borderRadius, p: 0.5 }}>
              <Skeleton
                variant="rectangular"
                height={imageHeight}
                sx={{ borderRadius: borderRadius }}
              />
              <Skeleton variant="text" width="80%" height={titleHeight} sx={{ mt: 1 }} />
              <Skeleton
                variant="text"
                width="50%"
                height={dateHeight}
                sx={{ mt: 0.5, bgcolor: "grey.200" }}
              />
            </Box>
          </Grid>
        ))}
      </Grid>
    );
  };

  const renderPromotionComponent = (item: any, index: number) => {
    if (!item.show && item.type !== "Header") return null;
    let com: React.ReactNode;
    switch (item.type) {
      case "VoucherHeader1":
        com = (
          <Box>
            <img
              src={item.backgroundImage}
              height={250}
              width={"100%"}
              style={{ objectFit: "cover" }}
            />
            <TabsSection
              activeTab={activeTab}
              onTabChange={setActiveTab}
              onRewardClick={handleRewardClick}
              onNavigateToVoucherTab={handleNavigateToVoucherTab}
              onEnterCodeClick={handleEnterCodeClick}
            />
          </Box>
        );
        break;
      case "Voucher1":
        com = (
          <Box key={index}>
            {(() => {
              switch (item.layout) {
                case "slide":
                  return <VoucherListSlide tabIndex={0} config={item} limit={item.limit} />;
                case "column":
                  return (
                    <Box paddingInline={2}>
                      <VoucherListColumn tabIndex={0} config={item} limit={item.limit} />
                    </Box>
                  );
                default:
                  return <VoucherListColumn tabIndex={0} config={item} limit={item.limit} />;
              }
            })()}
          </Box>
        );
        break;
      case "BannerWithBranch":
        com = (
          <Box key={index} pt={1}>
            <Banner item={item} />
          </Box>
        );
        break;
      case "ProductList2":
        com = (
          <Box key={index} px={2} pt={2}>
            <ProductListGridSliceV2
              title={item?.style?.title}
              item={item}
              onNavigateToProduction={() => {}}
            />
          </Box>
        );
        break;
      case keyComponent.ListNews2:
        if (item.style?.category == "grid") {
          com = (
            <Box key={index} p={2}>
              <ListItem
                title={item?.style?.title || ""}
                seeAll={() => onNavigateToPost(item?.style?.title)}
                btnAction={
                  <BtnAction
                    text={`Xem thêm`}
                    icon={<ArrowRightIcon fillColor={"#878787"} />}
                    eventAction={() => onNavigateToPost(item?.style?.title)}
                  />
                }
              >
                {Array.isArray(listArticle) && listArticle.length > 0 ? (
                  listArticle.map((news: INews, index) => (
                    <NewsItem
                      news={news}
                      key={String(index)}
                      titleFromParent={item?.style?.title}
                    />
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListItem>
            </Box>
          );
        } else if (item.style?.category == "slice") {
          com = (
            <Box key={index} py={2} px={2}>
              <ListSlider
                className={"list-news-slider"}
                title={item?.style?.title}
                titleStyle={{ color: appConfig.color.primary }}
                seeAll={() => onNavigateToPost(item?.style?.title)}
                btnAction={
                  <BtnAction
                    text={`Xem tất cả`}
                    styleOverride={{ color: appConfig.color.primary, fontSize: 12 }}
                    icon={<ArrowRightIcon fillColor={"#878787"} />}
                    eventAction={() => onNavigateToPost(item?.style?.title)}
                  />
                }
                sliceConfig={{
                  dots: false,
                  infinite: false,
                  slidesToShow: 1.5,
                  autoplay: false,
                  arrows: false,
                }}
              >
                {isLoadingNews ? (
                  renderNewsSkeletonLoading(true)
                ) : Array.isArray(listArticle) && listArticle.length > 0 ? (
                  listArticle.map((news: INews, index: number) => (
                    <NewsItemHorizontalV2
                      news={news}
                      key={index}
                      titleFromParent={item?.style?.title}
                      containerStyles={{
                        backgroundColor: COLORS.white,
                      }}
                    />
                  ))
                ) : (
                  <NoDataView content="Không có tin tức nào" />
                )}
              </ListSlider>
            </Box>
          );
        }
        break;
      case "ListNews":
        com = (
          <Box key={index} p={2}>
            <Box
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: 8,
              }}
            >
              <Typography style={{ fontWeight: 700, fontSize: 18 }}>
                {item?.style?.title || "Tin tức"}
              </Typography>
              <BtnAction
                text="Xem thêm"
                icon={<ArrowRightIcon fillColor={"#878787"} />}
                eventAction={() =>
                  navigate("/posts", { state: { title: item?.style?.title ?? "Tin tức" } })
                }
              />
            </Box>
            <ListNews list={listArticle} titleFromParent={item?.style?.title} />
          </Box>
        );
        break;

      default:
        com = null;
    }
    return com;
  };
  return (
    <Box
      sx={{
        minHeight: "100vh",
      }}
    >
      {promotion?.map((item, index) => renderPromotionComponent(item, index))}
    </Box>
  );
}

export default VoucherApp;

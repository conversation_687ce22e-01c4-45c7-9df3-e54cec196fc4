import React from "react";
import { VoucherApp } from "./VoucherNew";
import FrameContainerFull from "@/components/layout/ContainerFluid";
import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import { Router } from "@/constants/Route";
import { useSelector } from "react-redux";
import { useConfigApp } from "@/hooks/useConfigApp";
import { RootState } from "@/redux/store";
import { Coin } from "@/constants/IconSvg";
import { formatNumber } from "@/utils/common";
import VoucherTabsOptimized from "./VoucherTabsOptimized";
export function RightPoint() {
  const { user } = useSelector((state: RootState) => state.auth);
  const { color } = useConfigApp();

  return (
    <>
      {user?.point ? (
        <Typography
          sx={{
            color: color.primary,
            fontWeight: 500,
            fontSize: { xs: "0.8rem", sm: "0.9rem" },
            padding: { xs: "2px 6px", sm: "2px 8px" },
            borderRadius: 5,
            transition: "all 0.2s ease",
            cursor: "default",
            display: "flex",
            alignItems: "center",
            backgroundColor: "#FFF3E0",
            whiteSpace: "nowrap",
            minWidth: "fit-content",
            flexShrink: 0,
          }}
        >
          <Box
            sx={{
              marginRight: { xs: 0.3, sm: 0.5 },
              display: "flex",
              alignItems: "center",
              "& svg": {
                width: { xs: "16px", sm: "20px" },
                height: { xs: "16px", sm: "20px" },
              },
            }}
          >
            <Coin />
          </Box>
          {formatNumber(user.point)}
        </Typography>
      ) : (
        <Box></Box>
      )}
    </>
  );
}
export default function Voucher() {
  const { shopInfo, isLoading } = useSelector((state: RootState) => state.appInfo);
  const promotion = shopInfo?.template?.promotion;
  if (isLoading) return null;
  if (Array.isArray(promotion) && promotion.length > 0) {
    return (
      <FrameContainerFull title="Ưu đãi" rightComponent={<RightPoint />}>
        <VoucherApp />
      </FrameContainerFull>
    );
  }
  return (
    <FrameContainerFull title="Ưu đãi" rightComponent={<RightPoint />}>
      <VoucherTabsOptimized />
    </FrameContainerFull>
  );
}

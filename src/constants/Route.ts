import MembershipBenefit from "@/pages/Profile/MembershipBenefit";

export const Router = {
  login: "/login",
  register: "/register",
  otp: "/otp",
  homepage: "/",
  order: {
    index: "/order",
    detail: "/order/:id",
    f1Detail: "/order/other",
  },
  product: "/product",
  menu: "/menu",
  productCategory: "/product/:id?product_categories=",
  productDetail: "/product-detail/:id",
  search: "/search",
  // cart: "/cart",
  cartPayment: "/cartPayment",
  checkoutResult: "/checkout-result",
  post: {
    index: "/posts",
    detail: "/posts/:id",
  },
  notification: "/notification",

  profile: {
    index: "/profile",
    info: "/profile/info",
    memberShip: "profile/membership",
    payment: "/profile/payment-info",
    address: "/profile/address",
    policy: {
      index: "/profile/policy",
      detail: "/profile/policy/:id",
    },
    teamList: {
      index: "/profile/teamlist",
      detail: "/profile/teamlist/:id",
    },
    sigupPartner: "/profile/sigupPartner",
    membershipDetail: "/profile/membership-detail",
    earnPointsGuide: "/profile/earn-points-guide",
    pointHistory: "/profile/point-history",
    membershipBenefit: "/profile/membership-benefit",
  },
  voucher: {
    index: "/voucher",
    home: "/voucher-home",
    detail: "/voucher/:code",
    enterCode: "/voucher/enter-code",
  },
  promotion: {
    index: "/promotion",
  },
  point: "/point",
  refer: {
    index: "/refer",
    detail: "/refer/:id",
  },
  collabhistory: {
    index: "/collabhistory",
    detail: "/collabhistory/:id",
    commission: {
      index: "/collabhistory/commission",
    },
    members: {
      index: "/collabhistory/members",
      detail: "/collabhistory/members/:id",
    },
    orders: {
      index: "/collabhistory/orders",
    },
  },
  invoiceRequest: {
    index: "/invoice-request",
  },
  payment: {
    index: "/payment",
    detail: "payment/:id",
  },
  bankTransfer: {
    index: "/bankTransfer",
  },
  bankTransferV2: {
    index: "/bankTransferV2",
  },
  branch: {
    index: "/branch",
  },
  productCategoryPage: "/product-category",
  game: "/game",
};

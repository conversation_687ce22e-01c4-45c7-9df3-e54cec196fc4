import CheckIcon from "@/components/icon/CheckIcon";
import WarningIcon from "@/components/icon/WarningIcon";
import NoDataView from "@/components/UI/NoDataView";
import VoucherItemCollect from "@/components/voucher/itemcollect";
import VoucherItem, { VoucherItemCategory } from "@/components/voucher/VourcherItem";
import { ERROR_MESSAGE } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { ObjectHome } from "@/redux/slices/appInfo/appInfoSlice";
import { getUser } from "@/redux/slices/authen/authSlice";
import {
  getMyVoucherList,
  redeemPointToVoucher,
  VoucherDto,
} from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { showToast } from "@/utils/common";
import { useNavigate } from "@/utils/component-util";
import { Box, RadioGroup, Stack, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ArrowRightIcon from "../icon/ArrowRightIcon";

export default function VoucherHome({ item }: { item: ObjectHome }) {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const user = useSelector((state: RootState) => state.auth.user);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { myVoucherList, isLoading } = useSelector((state: RootState) => state.vouchers);

  const [showClearIcon, setShowClearIcon] = useState("none");
  const [voucherSelected, setVoucherSelected] = useState<VoucherDto | null>();
  const [searchKey, setSearchKey] = useState("");
  const { showAlert } = useAlert();
  const [voucherList, setVoucherList] = useState<any[]>([]);
  const data = { shopId: shopId, userId: user?.userId };
  useEffect(() => {
    if (data?.shopId) {
      dispatch(getMyVoucherList({ shopId: data.shopId, userId: data.userId }));
    }
  }, [shopId, user]);

  useEffect(() => {
    if (Array.isArray(myVoucherList)) {
      setVoucherList(myVoucherList);
    } else {
      setVoucherList([]);
    }
  }, [myVoucherList]);

  //   useEffect(() => {
  //     if (state && state.valueSearch) {
  //       setSearchKey(state.valueSearch);
  //     }
  //   }, [state]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
  };

  const handleClick = (): void => {
    setSearchKey("");
  };

  const handleSelectVoucher = (item: VoucherDto) => {
    setVoucherSelected(item);
  };

  const onChangePointToVoucher = async (voucherID) => {
    const res = await dispatch(
      redeemPointToVoucher({
        voucherId: voucherID,
      })
    ).unwrap();
    if (res.data) {
      if (res.message) {
        showAlert({
          title: ERROR_MESSAGE[res.message],
          content: "Xem lại tại kho voucher",
          icon: (
            <WarningIcon
              primaryColor={appConfig.color.primary}
              secondaryColor={appConfig.color.secondary}
            />
          ),
        });
      } else {
        dispatch(getUser());
        dispatch(getMyVoucherList({ shopId, userId: user?.userId }));
        showAlert({
          title: voucherSelected?.exchangePoints
            ? `Bạn đã đổi ${voucherSelected?.exchangePoints} điểm lấy voucher thành công`
            : "Bạn đã lưu voucher thành công",
          content: "Xem lại tại kho voucher của mình",
        });
      }
    } else {
      showToast({
        content: ERROR_MESSAGE[res.message] || "Đổi voucher không thành công, vui lòng thử lại sau",
        type: "error",
      });
    }
    setVoucherSelected(null);
  };

  const onApplyClick = (voucherSelect) => {
    showAlert({
      title: voucherSelect?.matchPoint ? "Đổi điểm lấy Voucher" : "Lưu Voucher",
      content: voucherSelect?.matchPoint
        ? `Bạn có muốn dùng ${voucherSelect?.matchPoint} điểm để đổi voucher này không?`
        : "Bạn có chắc chắn muốn lưu voucher này không?",
      icon: (
        <CheckIcon
          primaryColor={appConfig.color.primary}
          secondaryColor={appConfig.color.secondary}
        />
      ),
      buttons: [
        {
          title: "Huỷ",
          action: () => {
            setVoucherSelected(null);
          },
        },
        {
          title: "Xác nhận",
          action: () => {
            onChangePointToVoucher(voucherSelect.id);
          },
        },
      ],
    });
  };

  const filteredVoucherList = useMemo(() => {
    return Array.isArray(voucherList) ? voucherList : [];
  }, [voucherList]);

  function renderTitle() {
    return (
      item.style?.title && (
        <Box sx={styles.productionTitleContainer}>
          <Typography color={appConfig.color.primary} style={{ ...commonStyle.headline20 }}>
            {item.style?.title}
          </Typography>
          <Box
            mt={1}
            sx={{
              cursor: "pointer",
              display: "inline-flex",
              alignItems: "center",
              color: appConfig.color.primary,
            }}
            onClick={() => navigate(Router.voucher.index)}
          >
            <Typography fontSize={14} fontWeight={400}>
              {`Xem tất cả`}
            </Typography>
            <Stack ml={1} sx={{ height: 14 }}>
              <ArrowRightIcon fillColor={appConfig.color.primary} />
            </Stack>
          </Box>
        </Box>
      )
    );
  }

  function getVoucherCode(item: VoucherDto): string {
    return Array.isArray(item.voucherDetails) && item.voucherDetails.length > 0
      ? item.voucherDetails[0].voucherCode
      : item.voucherId;
  }

  return (
    <>
      <Stack sx={styles.contentContainer}>
        {renderTitle()}
        <Stack className="voucher-profile" width={"100%"} marginBlock={2}>
          <RadioGroup
            row
            aria-labelledby="demo-form-control-label-placement"
            name="position"
            defaultValue="top"
          >
            {filteredVoucherList.length > 0 ? (
              filteredVoucherList.slice(0, 4).map((item: any, index) => (
                <Box sx={{ paddingBottom: 2, width: "100%" }} key={item.voucherId}>
                  <VoucherItem
                    item={item}
                    category={VoucherItemCategory.LIST}
                    onSelectVoucher={() => handleSelectVoucher(item)}
                    isShowMatchPoint={!!item.exchangePoints}
                    onNavigateToDetail={() => {
                      const code = getVoucherCode(item);
                      navigate(`${Router.voucher.detail.replace(":code", code)}`, {
                        state: { voucher: item },
                      });
                    }}
                    isChecked={item.voucherId === voucherSelected?.voucherId}
                    onApplyClick={() => {
                      onApplyClick(item);
                    }}
                    myVoucherList={filteredVoucherList}
                  />
                </Box>
              ))
            ) : (
              <NoDataView content="Không có voucher" />
            )}
          </RadioGroup>
        </Stack>
      </Stack>
    </>
  );
}

const styles: Record<string, React.CSSProperties> = {
  productionTitleContainer: {
    display: "flex",

    marginTop: 0,
    justifyContent: "space-between",
    alignItems: "center",
  },
  contentContainer: {
    padding: "0 16px",
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 4,
    width: "100%",
  },
};

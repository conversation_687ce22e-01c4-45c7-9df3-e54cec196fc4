import { commonStyle } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import { ObjectHome } from "@/redux/slices/appInfo/appInfoSlice";
import { getProductByCategory } from "@/redux/slices/product/productListSlice";
import { setSearchCondition } from "@/redux/slices/product/productListSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { IProduct } from "@/types/product";
import { Box, Grid, Stack, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import BtnAction from "../../common/BtnAction";
import ArrowRightIcon from "../../icon/ArrowRightIcon";
import ProductItemV2 from "../../products/item/ProductItemRetail";
import NoDataView from "../../UI/NoDataView";
import ListItem from "../ListItemShow";
import ListSlider from "../SliceItemShow";
import ProductItemFnB from "@/components/products/item/ProductItemFnB";
import { useNavigate } from "@/utils/component-util";

const ProductListGridSliceRetail = ({
  title,
  item,
  onNavigateToProduction,
}: {
  title?: string;
  item: ObjectHome;
  onNavigateToProduction: () => void;
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const [listProduct, setListProduct] = useState<IProduct[]>([]);
  const [imageError, setImageError] = useState(false);
  const appConfig = useConfigApp();
  const data = {
    shopId,
    categoryId: item.categoryId,
  };
  const fetchProductList = async () => {
    const res = await dispatch(getProductByCategory(data));
    setListProduct(res?.payload?.data || []);
  };
  useEffect(() => {
    fetchProductList();
  }, []);
  const navigate = useNavigate();
  const handleSeeAll = (categoryId: string) => {
    if (categoryId) {
      dispatch(setSearchCondition({ categoryId }));
      navigate("/menu", { state: { categoryId } });
    }
  };

  if (item.style?.category === "grid2") {
    const hasBgImage = item?.style?.backgroundImage && !imageError;
    const bgImage = hasBgImage ? `url(${item.style.backgroundImage})` : undefined;
    const bgColor =
      !hasBgImage && item?.style?.backgroundColor ? item.style.backgroundColor : "#fff";

    return (
      <Box
        sx={{
          width: "calc(100% + 32px)",
          marginLeft: "-16px",
          backgroundColor: bgColor,
          backgroundImage: bgImage,
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
          backgroundPosition: "center",
        }}
        onErrorCapture={() => setImageError(true)}
      >
        <Box sx={{ width: "100%", maxWidth: 1280, mx: "auto", py: 2, px: 2 }}>
          <DealSectionHeader
            onSeeAll={handleSeeAll}
            categoryId={item.categoryId}
            title={title}
            subTitle={item?.style?.subtitle}
            divider={item?.style?.divider}
            moreText={item?.style?.moreText}
          />
          <Grid container spacing={2}>
            {listProduct.length > 0 ? (
              listProduct.slice(0, 6).map((product: IProduct) => (
                <Grid item xs={6} key={product.itemsCode}>
                  {item.style?.type === "prodrating" ? (
                    <ProductItemFnB item={product} discountType={item.style?.discountType} />
                  ) : (
                    <ProductItemV2 item={product} discountType={item.style?.discountType} />
                  )}
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <NoDataView content="Không có sản phẩm nào" />
              </Grid>
            )}
          </Grid>
        </Box>
      </Box>
    );
  }

  if (item.style?.category === "grid") {
    return (
      <ListItem
        title={title || ""}
        seeAll={onNavigateToProduction}
        titleOverrideStyle={{ color: appConfig.color.primary }}
        btnAction={
          <BtnAction
            text="Xem tất cả "
            icon={<ArrowRightIcon fillColor={appConfig.color.primary} />}
            eventAction={onNavigateToProduction}
            styleOverride={{
              color: appConfig.color.primary,
              fontSize: 12,
            }}
          />
        }
      >
        {listProduct.length > 0 ? (
          listProduct.slice(0, 6).map((product) => (
            <Grid item xs={6} key={product.itemsCode}>
              {item.style?.type === "prodrating" ? (
                <ProductItemFnB item={product} discountType={item.style?.discountType} />
              ) : (
                <ProductItemV2 item={product} discountType={item.style?.discountType} />
              )}
            </Grid>
          ))
        ) : (
          <NoDataView content="Không có sản phẩm nào" />
        )}
      </ListItem>
    );
  }

  return (
    <>
      <DealSectionHeader
        onSeeAll={handleSeeAll}
        categoryId={item.categoryId}
        title={title}
        subTitle={item?.style?.subtitle}
        divider={item?.style?.divider}
        moreText={item?.style?.moreText}
      />
      <ListSlider
        title=""
        seeAll={() => {
          handleSeeAll(item.categoryId || "");
        }}
        sliceConfig={{
          dots: false,
          infinite: false,
          slidesToShow: 2.2,
          autoplay: false,
          arrows: false,
        }}
      >
        {listProduct.length > 0 ? (
          listProduct.map((product) => (
            <Box key={product.itemsCode} className="box-product-item">
              {item.style?.type === "prodrating" ? (
                <ProductItemFnB item={product} discountType={item.style?.discountType} />
              ) : (
                <ProductItemV2 item={product} discountType={item.style?.discountType} />
              )}
            </Box>
          ))
        ) : (
          <NoDataView content="Không có sản phẩm nào" />
        )}
      </ListSlider>
    </>
  );
};

export default ProductListGridSliceRetail;

const DealSectionHeader = ({
  onSeeAll,
  categoryId,
  title,
  subTitle,
  divider,
  moreText,
}: {
  onSeeAll: (categoryId: string) => void;
  categoryId?: string;
  title?: string;
  subTitle?: string;
  divider?: { show?: boolean; color?: string };
  moreText?: string;
}) => {
  const appConfig = useConfigApp();

  return (
    <Box textAlign="center" mb={2}>
      <Stack direction="row" alignItems="center" justifyContent="center" spacing={2}>
        {divider?.show !== false && (
          <Box
            sx={{
              height: 2,
              width: 40,
              background: divider?.color
                ? `linear-gradient(to right, transparent, ${divider.color})`
                : "linear-gradient(to right, transparent, red)",
            }}
          />
        )}
        <Typography sx={{ ...commonStyle.headline20 }} color={appConfig.color.primary}>
          {title}
        </Typography>
        {divider?.show !== false && (
          <Box
            sx={{
              height: 2,
              width: 40,
              background: divider?.color
                ? `linear-gradient(to left, transparent, ${divider.color})`
                : "linear-gradient(to left, transparent, red)",
            }}
          />
        )}
      </Stack>
      {subTitle && (
        <Typography sx={{ fontSize: 12 }} color={appConfig.color.primary}>
          {subTitle}
        </Typography>
      )}
      <Box
        mt={1}
        sx={{
          cursor: "pointer",
          display: "inline-flex",
          alignItems: "center",
          color: appConfig.color.primary,
        }}
        onClick={() => onSeeAll(categoryId || "")}
      >
        <Typography fontSize={14} fontWeight={400}>
          {moreText || "Xem tất cả"}
        </Typography>
        <Stack ml={1} sx={{ height: 14 }}>
          <ArrowRightIcon fillColor={appConfig.color.primary} />
        </Stack>
      </Box>
    </Box>
  );
};

import React, { useEffect, useState, useRef, useCallback } from "react";
import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import { AppDispatch, RootState } from "../../redux/store";
import { useSelector } from "react-redux";
import { IProduct } from "../../types/product";
import NoDataView from "../UI/NoDataView";
import VerticalProductItem from "./VerticalProductItem";
import InfiniteScroll from "../UI/InfiniteScroll";
import { useDispatch } from "react-redux";
import { getProductListByCategory } from "@/redux/slices/product/productListSlice";
import ProductItemSkeleton from "./ProductItemSkeleton";

interface ListType {
  products: IProduct[];
  pagination: {
    skip: number;
    limit: number;
    total: number;
  };
}

interface ProductOneColumnProps {
  initialCategoryId?: number;
  onUpdateCount?: (count: number) => void;
  disableInfiniteScroll?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

const ProductOneColumn: React.FC<ProductOneColumnProps> = ({
  initialCategoryId,
  onUpdateCount,
  disableInfiniteScroll = false,
}) => {
  const { list, isLoading, isInitialLoading, sortBy, searchCondition, productCache } = useSelector(
    (state: RootState) => state.productList
  );

  const dispatch = useDispatch<AppDispatch>();

  const [localList, setLocalList] = useState<ListType>(list);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [showNoData, setShowNoData] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [visibleCount, setVisibleCount] = useState(20);
  const listContainerRef = useRef<HTMLDivElement | null>(null);

  // Tham chiếu đến thời gian loading
  const loadingTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const loadingStartTimeRef = useRef<number>(0);

  // Kiểm tra xem danh mục hiện tại đã có trong cache chưa
  const getCacheKey = (condition: any) => {
    return `${condition.categoryId || ""}_${condition.parentId || ""}_${condition.search || ""}`;
  };

  const currentCacheKey = getCacheKey(searchCondition);
  const isCached = productCache && productCache[currentCacheKey];

  // Theo dõi trạng thái loading
  useEffect(() => {
    if (isInitialLoading && !isCached) {
      // Bắt đầu đếm thời gian loading
      loadingStartTimeRef.current = Date.now();

      // Thiết lập timer cho việc hiển thị "Không có sản phẩm"
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }

      loadingTimerRef.current = setTimeout(() => {
        if (isInitialLoading) {
          setInitialLoadComplete(true);
          setShowNoData(true);
        }
      }, 1500); // Giảm thời gian xuống 1.5 giây
    }

    // Nếu không còn loading, xóa timer
    if (!isInitialLoading && loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = null;
    }

    // Đảm bảo hiển thị sản phẩm ngay khi có dữ liệu, ngay cả khi vẫn đang loading
    if (list.products.length > 0 && !initialLoadComplete) {
      // Xóa timer nếu có
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
        loadingTimerRef.current = null;
      }

      setInitialLoadComplete(true);
      setShowNoData(false);
    }
  }, [isInitialLoading, isCached, list.products.length, initialLoadComplete]);

  // Dọn dẹp timer khi component unmount
  useEffect(() => {
    return () => {
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }
    };
  }, []);

  // Apply sorting and filtering when list, sortBy, or searchCondition changes
  useEffect(() => {
    // Start with the original list
    let processedProducts = [...list.products];

    // Apply price filtering if needed
    if (
      searchCondition.priceFilter &&
      (searchCondition.minPrice !== undefined || searchCondition.maxPrice !== undefined)
    ) {
      const minPrice = searchCondition.minPrice || 0;
      const maxPrice = searchCondition.maxPrice || 5000000; // Giới hạn giá tối đa 5 triệu

      processedProducts = processedProducts.filter((item) => {
        const itemPrice = item.price || item.listVariant[0]?.price || 0;
        return itemPrice >= minPrice && itemPrice <= maxPrice;
      });
    }

    // Apply sorting if needed
    if (sortBy && sortBy.field && sortBy.order !== "default") {
      processedProducts.sort((a, b) => {
        const a_price = a.price || a.listVariant[0]?.price || 0;
        const b_price = b.price || b.listVariant[0]?.price || 0;

        return sortBy.order === "asc" ? a_price - b_price : b_price - a_price;
      });
    }

    // Update local list with processed products
    setLocalList({
      ...list,
      products: processedProducts,
      pagination: {
        ...list.pagination,
        total: processedProducts.length,
      },
    });

    // Report the filtered count back to parent
    if (onUpdateCount) {
      onUpdateCount(processedProducts.length);
    }

    // Mark initial load as complete if not loading anymore
    if (!isInitialLoading && !initialLoadComplete) {
      setInitialLoadComplete(true);

      // Nếu không có sản phẩm sau khi tải xong, hiển thị "Không có sản phẩm" ngay lập tức
      if (list.products.length === 0) {
        setShowNoData(true);
      } else {
        setShowNoData(false);
      }

      // Xóa timer nếu có
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
        loadingTimerRef.current = null;
      }
    }

    // Nếu có sản phẩm, đảm bảo không hiển thị "Không có sản phẩm"
    if (list.products.length > 0) {
      setShowNoData(false);
    }

    // Nếu danh sách sản phẩm rỗng và đã hoàn thành loading, hiển thị "Không có sản phẩm"
    if (list.products.length === 0 && !isInitialLoading) {
      setShowNoData(true);
    }

    // Kết thúc trạng thái loading khi tải thêm dữ liệu
    // setLoadingMore(false); // Trong useEffect KHÔNG set loadingMore(false) nữa khi tải thêm sản phẩm
  }, [list, sortBy, searchCondition, onUpdateCount, isInitialLoading]);

  // Reset visibleCount khi filter/search thay đổi
  useEffect(() => {
    setVisibleCount(40);
  }, [list, sortBy, searchCondition]);

  // Hàm xử lý scroll để lazy render thêm sản phẩm
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      setVisibleCount((prev) => Math.min(prev + 20, localList.products.length));
    }
  };

  // Hàm được cải thiện để tải thêm sản phẩm khi cuộn
  const fetchMoreProducts = useCallback(() => {
    if (disableInfiniteScroll || loadingMore) return;

    setLoadingMore(true);

    dispatch(async (_, getState) => {
      const state = getState().productList?.list;
      if (!state) {
        setLoadingMore(false);
        return;
      }

      // Kiểm tra xem còn sản phẩm để tải không dựa trên pagination
      const currentItemCount = state.products.length;
      const totalItems = state.pagination.total;

      // Nếu đã tải hết hoặc gần hết sản phẩm, không cần tải thêm
      if (currentItemCount >= totalItems) {
        setLoadingMore(false);
        return;
      }

      // Tính toán số lượng sản phẩm còn lại để tải
      const remaining = totalItems - currentItemCount;
      // Chọn giá trị nhỏ hơn giữa limit ban đầu và số lượng còn lại
      const nextLimit = Math.min(state.pagination.limit, remaining);

      if (nextLimit <= 0) {
        setLoadingMore(false);
        return;
      }

      // Gọi API để tải thêm sản phẩm
      await dispatch(
        getProductListByCategory({
          ...searchCondition,
          skip: currentItemCount,
          limit: nextLimit,
          shopId: searchCondition.shopId,
        })
      );
      setTimeout(() => setLoadingMore(false), 1000);
    });
  }, [disableInfiniteScroll, loadingMore, dispatch, searchCondition]);

  // Render products directly if infinite scroll is disabled
  const renderProductList = () => {
    return (
      <>
        {localList.products.slice(0, visibleCount).map((item: IProduct, index: number) => (
          <Box
            key={item.itemsCode}
            sx={{
              marginBottom: index === localList.products.length - 1 ? "16px" : 0,
              width: "100%",
            }}
          >
            <VerticalProductItem item={item} />
            {/* <ProductItemSkeleton /> */}
          </Box>
        ))}
      </>
    );
  };

  const renderSkeletons = (count = 3) => (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        py: 2,
        width: "100%",
      }}
    >
      {Array.from({ length: count }).map((_, idx) => (
        <ProductItemSkeleton key={idx} />
      ))}
    </Box>
  );

  // Thêm hàm renderLoadingIndicator để hiển thị loading thay vì skeleton khi scroll
  const renderLoadingIndicator = () => {
    if (!loadingMore) return null;

    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          py: 2,
          width: "100%",
        }}
      >
        <CircularProgress size={30} sx={{ mb: 1 }} />
        <Typography variant="caption" sx={{ color: "#666" }}>
          Đang tải thêm sản phẩm...
        </Typography>
      </Box>
    );
  };

  // Render cho virtualized item
  const renderVirtualizedItem = ({
    index,
    style,
    data,
  }: {
    index: number;
    style: React.CSSProperties;
    data: IProduct[];
  }) => {
    // Kiểm tra nếu index vượt quá số sản phẩm hiện có
    if (index >= data.length) {
      return (
        <div style={style}>
          <ProductItemSkeleton />
        </div>
      );
    }

    const item = data[index];
    return (
      <div style={{ ...style, width: "100%" }}>
        <VerticalProductItem item={item} />
      </div>
    );
  };

  // Xác định trạng thái hiển thị dựa trên các điều kiện
  const shouldShowSkeleton = (!initialLoadComplete || isInitialLoading) && !isCached;
  const shouldShowProducts = Array.isArray(localList.products) && localList.products.length > 0;
  const shouldShowNoData = showNoData && !shouldShowProducts && !shouldShowSkeleton;
  const hasMore = list.pagination.skip + list.pagination.limit < list.pagination.total;

  // Tính thời gian đã loading
  const loadingTime = Date.now() - loadingStartTimeRef.current;

  // Nếu đã loading quá 2 giây mà vẫn chưa có kết quả, hiển thị "Không có sản phẩm"
  if (shouldShowSkeleton && loadingTime > 2000 && !shouldShowProducts) {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Box
          sx={{
            width: "100%",
            maxWidth: { xs: "100%", sm: "600px", md: "800px" },
            margin: "0 auto",
            padding: "0 8px",
          }}
        >
          <NoDataView content="Không có sản phẩm nào" />
        </Box>
      </Box>
    );
  }

  return (
    <Box
      ref={listContainerRef}
      onScroll={handleScroll}
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        justifyContent: "center",
        overflowY: "auto",
      }}
    >
      <Box
        sx={{
          width: "100%",
          maxWidth: { xs: "100%", sm: "600px", md: "800px" },
          margin: "0 auto",
          padding: "0 0px",
        }}
      >
        {shouldShowSkeleton ? (
          renderSkeletons(8)
        ) : shouldShowProducts ? (
          disableInfiniteScroll ? (
            renderProductList()
          ) : (
            <InfiniteScroll
              loader={loadingMore ? renderSkeletons(3) : null}
              className=""
              fetchMore={fetchMoreProducts}
              hasMore={hasMore && !disableInfiniteScroll}
              endMessage={null}
            >
              {renderProductList()}
            </InfiniteScroll>
          )
        ) : loadingMore || isInitialLoading ? (
          renderSkeletons(8)
        ) : shouldShowNoData ? (
          <NoDataView content="Không có sản phẩm nào" />
        ) : (
          renderSkeletons(8)
        )}
      </Box>
    </Box>
  );
};

export default React.memo(ProductOneColumn);

const styles: Record<string, React.CSSProperties> = {
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 4,
    width: "100%",
  },
  itemContainer: {
    textAlign: "left",
    paddingBottom: 12,
  },
};

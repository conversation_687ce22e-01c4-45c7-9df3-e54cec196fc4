import { useConfigApp } from "@/hooks/useConfigApp";
import { Badge, BottomNavigation, BottomNavigationAction, Paper } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { NO_BOTTOM_NAVIGATION_PAGES } from "../../constants/Const";
import { useCart } from "../../hooks/useCart";
import { BottomTabItem, useBottomTabs } from "./BottomTabs";

export default function WebBottomTabNavigation() {
  const appConfig = useConfigApp();
  const { cartPayment } = useCart();
  const [activeTab, setActiveTab] = useState("chat");
  const location = useLocation();
  const navigate = useNavigate();
  const bottomTabs = useBottomTabs();

  useEffect(() => {
    setActiveTab(location.pathname);
  }, [location]);

  const totalProduct = useMemo(() => {
    if (!Array.isArray(cartPayment?.listItems) || !cartPayment?.listItems.length) return 0;
    return cartPayment?.listItems.reduce((acc, item) => acc + item.quantity, 0);
  }, [cartPayment]);

  const noBottomNav = useMemo(() => {
    if (/^\/myVoucher\/\d+$/.test(location.pathname)) return true;
    if (/^\/voucher\/\d+$/.test(location.pathname)) return true;
    return NO_BOTTOM_NAVIGATION_PAGES.some((path) => location.pathname.includes(path));
  }, [location]);

  if (noBottomNav) {
    return null;
  }

  const renderIcon = (item: BottomTabItem) => (
    <Badge
      badgeContent={item.value === "/cartPayment" && totalProduct ? totalProduct : 0}
      color="error"
      sx={{
        span: {
          height: "18px",
          minWidth: "18px",
        },
        flexDirection: "column",
      }}
    >
      {item.icon && typeof item.icon === "function" ? (
        <item.icon fillColor={activeTab === item.value} />
      ) : null}
      <div style={{ height: 2 }} />
    </Badge>
  );

  return (
    <Paper
      className="web-bottom-tab"
      sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
      elevation={3}
    >
      <BottomNavigation
        showLabels
        value={activeTab}
        onChange={(event, newValue) => {
          setActiveTab(newValue);
        }}
      >
        {bottomTabs.map((item) => (
          <BottomNavigationAction
            className="web-bottom-tab-item"
            label={item.label}
            onClick={() => navigate(`${item.value}`)}
            icon={renderIcon(item)}
            key={item.value}
            style={activeTab === item.value ? { color: appConfig.color.primary } : {}}
          />
        ))}
      </BottomNavigation>
    </Paper>
  );
}

import { Icon } from "@/constants/Assets";
import { IMenu } from "@/types/menu";
import { Box, Stack } from "@mui/material";
import React from "react";

export default function MenuCategoryItemRetailV2({
  item,
  style,
  shopInfo,
  category,
}: {
  item?: IMenu;
  style?: { radius?: number; fontSize?: number; fontWeight?: number };
  shopInfo?: { businessType: string };
  category?: string;
}) {
  if (!item) return null;

  const handleError = (e) => {
    e.target.src = Icon.errorImage;
  };
  let imageSrc = item.image && item.image.link ? item.image.link : item.icon;
  if (!imageSrc) imageSrc = Icon.errorImage;

  if (category === "grid2") {
    return (
      <Box
        sx={{
          width: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <img
          src={imageSrc}
          alt={item.title}
          style={{
            width: "100%",
            height: "auto",
            objectFit: "contain",
          }}
          onError={handleError}
        />
      </Box>
    );
  }

  return (
    <Stack
      sx={{
        alignItems: "center",
        justifyContent: "center",
        width: "100%",
        padding: 0,
      }}
    >
      <Stack
        sx={{
          alignItems: "center",
          justifyContent: "center",
          width: "80px",
          height: "80px",
        }}
      >
        <img
          src={imageSrc}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "contain",
            aspectRatio: "1 / 1",
          }}
          onError={handleError}
        />
      </Stack>
      {/* <Typography style={styles.titleStyle}>{item.title}</Typography> */}
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  container: {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    padding: "0px",
  },
  iconStyle: {
    width: "160%",
    height: "160%",
    aspectRatio: "1 / 1",
    objectFit: "contain",
  },
  titleStyle: {
    marginTop: 5,
    fontSize: 12,
    textAlign: "center",
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 2,
    overflow: "hidden",
    textOverflow: "ellipsis",
    fontWeight: "700",
    wordBreak: "break-word",
    lineHeight: "1.5em",
    maxHeight: "3em",
    color: "#8E8E8E",
  },
};

name: Deploy Web App Stg

on:
  push:
    branches:
      - staging
      - "staging*"

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: '--max_old_space_size=4096'
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'yarn'
      - run: |
          yarn
          yarn build:web-staging
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          zip -r dist.zip dist

      - name: copy file via ssh password
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.STG_HOST }}
          username: ${{ secrets.STG_USERNAME }}
          password: ${{ secrets.STG_PASSWORD }}
          source: "dist.zip"
          target: "/var/www/webapp/"

      - name: executing remote ssh commands using password
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STG_HOST }}
          username: ${{ secrets.STG_USERNAME }}
          password: ${{ secrets.STG_PASSWORD }}
          script: |
            apt-get install unzip
            mkdir -p /var/www/webapp
            cd /var/www/webapp
            unzip -o dist.zip

      - name: Send noti
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] Build EvoRetail Web STG success - ${{ github.event.head_commit.message }}'

